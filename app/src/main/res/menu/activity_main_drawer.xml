<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="navigation_view">

    <group android:checkableBehavior="single">
        <item
            android:id="@+id/nav_home"
            android:icon="@drawable/ic_menu_home_48px"
            android:title="@string/menu_home" />
        <item
            android:id="@+id/nav_gallery"
            android:icon="@drawable/ic_menu_gallery"
            android:title="@string/menu_gallery" />
        <item
            android:id="@+id/nav_exposurecalculation"
            android:icon="@drawable/ic_menu_exposure_calculation_48px"
            android:title="Expoziční čas" />
        <item
            android:id="@+id/nav_iqiselection"
            android:icon="@drawable/ic_wireiqi_48px"
            android:title="IQI selection" />
        <item
            android:id="@+id/nav_wireiqi"
            android:icon="@drawable/ic_wireiqi_48px"
            android:title="Wire IQI" />
        <item
            android:id="@+id/nav_exposurecorrection"
            android:icon="@drawable/ic_menu_exposure_48px"
            android:title="@string/menu_exposurecorrection" />
        <item
            android:id="@+id/nav_decay"
            android:icon="@drawable/ic_menu_radioactive_circle_outline"
            android:title="@string/menu_decay" />
        <item
            android:id="@+id/nav_unsharp"
            android:icon="@drawable/ic_unsharp_48px"
            android:title="@string/menu_unsharp" />
        <item
            android:id="@+id/nav_stopwatch"
            android:icon="@drawable/ic_menu_timer_48px"
            android:title="@string/menu_stopwatch" />
        <item
            android:id="@+id/nav_countdown"
            android:icon="@drawable/ic_menu_av_timer_48px"
            android:title="@string/menu_countdown" />
        <item
            android:id="@+id/nav_matcalc"
            android:icon="@drawable/ic_menu_calculate_48px"
            android:title="@string/menu_matcalc" />
        <item
            android:id="@+id/nav_settings"
            android:icon="@android:drawable/ic_menu_preferences"
            android:title="@string/menu_settings" />
    </group>
</menu>