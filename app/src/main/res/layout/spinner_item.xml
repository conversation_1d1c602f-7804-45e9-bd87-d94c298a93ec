<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:background="@android:color/white"
    android:paddingStart="12dp"
    android:paddingEnd="12dp">

    <TextView
        android:id="@android:id/text1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@android:color/black"
        android:textSize="16sp"
        android:gravity="center_vertical" />

    <ImageView
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_arrow_drop_down_24"
        android:contentDescription="Rozbalit"
        android:layout_marginStart="8dp" />

</LinearLayout>
