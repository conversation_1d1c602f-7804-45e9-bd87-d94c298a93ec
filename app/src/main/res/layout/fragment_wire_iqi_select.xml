<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Nadpis -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Výběr IQI měrky"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="20dp" />

        <!-- Vstupní parametry Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="14dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="#424242">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="12dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Vstupní parametry"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="12dp" />

                <!-- Materiál objektu -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Materiál zkoušeného objektu:"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/spinnerMaterial"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginBottom="12dp"
                    android:background="@android:drawable/screen_background_light_transparent" />

                <!-- Tloušťka -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Tloušťka objektu (mm):"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="8dp" />

                <EditText
                    android:id="@+id/editTextThickness"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginBottom="12dp"
                    android:inputType="numberDecimal"
                    android:hint="Zadejte tloušťku v mm"
                    android:textColor="@android:color/white"
                    android:background="@android:drawable/screen_background_light_transparent"
                    android:padding="8dp" />

                <!-- Třída zkoušky -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Požadovaná třída zkoušky:"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/spinnerTestingClass"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginBottom="12dp"
                    android:background="@android:drawable/screen_background_light_transparent" />

                <!-- Typ měrky -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Typ měrky:"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/spinnerIqiType"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginBottom="12dp"
                    android:background="@android:drawable/screen_background_light_transparent" />

                <!-- Norma výběru IQI -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Norma pro výběr IQI:"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/spinnerStandard"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginBottom="12dp"
                    android:background="@android:drawable/screen_background_light_transparent" />

                <!-- Technika snímkování -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Technika snímkování:"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/spinnerTechnique"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginBottom="12dp"
                    android:background="@android:drawable/screen_background_light_transparent" />

                <!-- Umístění měrky -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Umístění měrky:"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/spinnerPlacement"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginBottom="12dp"
                    android:background="@android:drawable/screen_background_light_transparent" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Tlačítko pro výpočet -->
        <Button
            android:id="@+id/buttonCalculate"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginBottom="12dp"
            android:text="Určit IQI měrku"
            android:textSize="16sp"
            android:textStyle="bold"
            android:backgroundTint="#4CAF50" />

        <!-- Výsledek Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardResult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="#424242">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="12dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Výsledek"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/textViewResult"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/textViewMaterial"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/textViewDetails"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textColor="#CCCCCC" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>
</ScrollView>