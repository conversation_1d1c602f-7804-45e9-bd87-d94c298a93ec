<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/nav_home">

    <fragment
        android:id="@+id/nav_home"
        android:name="com.example.stopky.ui.home.HomeFragment"
        android:label="@string/menu_home"
        tools:layout="@layout/fragment_home" />

    <fragment
        android:id="@+id/nav_gallery"
        android:name="com.example.stopky.ui.gallery.GalleryFragment"
        android:label="@string/menu_gallery"
        tools:layout="@layout/fragment_gallery" />

    <fragment
        android:id="@+id/nav_exposurecalculation"
        android:name="com.example.stopky.ui.exposurecalculation.ExposureCalculationFragment"
        android:label="Expoziční čas"
        tools:layout="@layout/fragment_exposure_calculation" />

    <fragment
        android:id="@+id/nav_iqiselection"
        android:name="com.example.stopky.ui.iqiselection.IQISelectionFragment"
        android:label="IQI selection"
        tools:layout="@layout/fragment_wire_iqi_select" />

    <fragment
        android:id="@+id/nav_wireiqi"
        android:name="com.example.stopky.ui.wireiqi.WireIQIFragment"
        android:label="Wire IQI"
        tools:layout="@layout/fragment_wire_iqi" />

    <fragment
        android:id="@+id/nav_exposurecorrection"
        android:name="com.example.stopky.ui.exposurecorrection.ExposureCorrectionFragment"
        android:label="@string/menu_exposurecorrection"
        tools:layout="@layout/fragment_exposurecorrection" />

    <fragment
        android:id="@+id/nav_decay"
        android:name="com.example.stopky.ui.decay.DecayFragment"
        android:label="@string/menu_decay"
        tools:layout="@layout/fragment_decay" />

    <fragment
        android:id="@+id/nav_unsharp"
        android:name="com.example.stopky.ui.unsharp.UnsharpFragment"
        android:label="@string/menu_unsharp"
        tools:layout="@layout/fragment_unsharp" />

    <fragment
        android:id="@+id/nav_stopwatch"
        android:name="com.example.stopky.ui.stopwatch.StopwatchFragment"
        android:label="@string/menu_stopwatch"
        tools:layout="@layout/fragment_stopwatch" />

    <fragment
        android:id="@+id/nav_countdown"
        android:name="com.example.stopky.ui.countdown.CountdownFragment"
        android:label="@string/menu_countdown"
        tools:layout="@layout/fragment_countdown" />

    <fragment
        android:id="@+id/nav_matcalc"
        android:name="com.example.stopky.ui.matcalc.MatCalcFragment"
        android:label="@string/menu_matcalc"
        tools:layout="@layout/fragment_mat_calc" />

    <fragment
        android:id="@+id/nav_settings"
        android:name="com.example.stopky.ui.settings.SettingsFragment"
        android:label="@string/menu_settings"
        tools:layout="@layout/fragment_settings" />

</navigation>