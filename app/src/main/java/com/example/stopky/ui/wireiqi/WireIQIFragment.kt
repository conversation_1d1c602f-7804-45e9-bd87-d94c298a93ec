package com.example.stopky.ui.wireiqi

import android.content.SharedPreferences
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.preference.PreferenceManager
import com.example.stopky.R

class WireIQIFragment : Fragment() {

    private val iqiValues = arrayOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19)
    private val iqiFactors = arrayOf(3.2, 2.5, 2.0, 1.6, 1.25, 1.0, 0.8, 0.63, 0.5, 0.4, 0.32, 0.25, 0.2, 0.16, 0.125, 0.1, 0.08, 0.063, 0.05)

    private lateinit var spinnerIqi: Spinner
    private lateinit var editMaterialThickness: EditText
    private lateinit var textSensitivityResult: TextView

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_wire_iqi, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Nastavení rozbalovací karty
        setupExpandableCard()

        spinnerIqi = view.findViewById(R.id.spinner_iqi)
        editMaterialThickness = view.findViewById(R.id.edit_material_thickness)
        textSensitivityResult = view.findViewById(R.id.text_sensitivity_result)

        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, iqiValues)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerIqi.adapter = adapter

        spinnerIqi.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>, v: View?, position: Int, id: Long) {
                calculateAndShowResult()
            }
            override fun onNothingSelected(parent: AdapterView<*>) {}
        }

        editMaterialThickness.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                calculateAndShowResult()
            }
            override fun afterTextChanged(s: Editable?) {}
        })
    }

    private fun calculateAndShowResult() {
        val iqiIndex = spinnerIqi.selectedItemPosition
        val factor = iqiFactors[iqiIndex]
        val thicknessStr = editMaterialThickness.text.toString()
        if (thicknessStr.isEmpty()) {
            textSensitivityResult.text = "0.0 %"
            return
        }
        val thickness = thicknessStr.toDoubleOrNull()
        if (thickness == null || thickness <= 0) {
            textSensitivityResult.text = "0.0 %"
            return
        }
        val result = factor / thickness * 100.0
        textSensitivityResult.text = String.format("%.2f %%", result)
    }

    private fun setupExpandableCard() {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())

        // Nastavení karty Wire IQI
        setupSingleExpandableCard(
            R.id.cardHeaderWireIqi,
            R.id.cardContentWireIqi,
            R.id.iconExpandWireIqi,
            "wire_iqi_card_expanded",
            false, // Výchozí: sbaleno
            prefs
        )
    }

    private fun setupSingleExpandableCard(
        headerResId: Int,
        contentResId: Int,
        iconResId: Int,
        prefKey: String,
        defaultExpanded: Boolean,
        prefs: SharedPreferences
    ) {
        val cardHeader = view?.findViewById<View>(headerResId)
        val cardContent = view?.findViewById<View>(contentResId)
        val expandIcon = view?.findViewById<View>(iconResId)

        // Načíst uložený stav karty
        val isExpanded = prefs.getBoolean(prefKey, defaultExpanded)

        // Nastavit počáteční stav
        cardContent?.visibility = if (isExpanded) View.VISIBLE else View.GONE
        expandIcon?.rotation = if (isExpanded) 180f else 0f

        // Nastavit click listener
        cardHeader?.setOnClickListener {
            val isCurrentlyExpanded = cardContent?.visibility == View.VISIBLE
            val newExpanded = !isCurrentlyExpanded

            // Animace rozbalení/sbalení
            if (newExpanded) {
                cardContent?.visibility = View.VISIBLE
                expandIcon?.animate()?.rotation(180f)?.duration = 200
            } else {
                cardContent?.visibility = View.GONE
                expandIcon?.animate()?.rotation(0f)?.duration = 200
            }

            // Uložit stav
            prefs.edit().putBoolean(prefKey, newExpanded).apply()
        }
    }
}