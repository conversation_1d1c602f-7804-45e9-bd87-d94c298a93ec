package com.example.stopky.ui.iqiselection

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.cardview.widget.CardView
import androidx.fragment.app.Fragment
import com.example.stopky.R
import org.json.JSONObject
import java.io.IOException

class IQISelectionFragment : Fragment() {

    // UI prvky
    private lateinit var spinnerMaterial: Spinner
    private lateinit var editTextThickness: EditText
    private lateinit var spinnerTestingClass: Spinner
    private lateinit var spinnerIqiType: Spinner
    private lateinit var spinnerStandard: Spinner
    private lateinit var spinnerTechnique: Spinner
    private lateinit var spinnerPlacement: Spinner
    private lateinit var buttonCalculate: Button
    private lateinit var cardResult: CardView
    private lateinit var textViewResult: TextView
    private lateinit var textViewMaterial: TextView
    private lateinit var textViewDetails: TextView

    // Data
    private var iqiData: JSONObject? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val root = inflater.inflate(R.layout.fragment_wire_iqi_select, container, false)

        initializeViews(root)
        loadIQIData()
        setupSpinners()
        setupCalculateButton()

        return root
    }

    private fun initializeViews(root: View) {
        spinnerMaterial = root.findViewById(R.id.spinnerMaterial)
        editTextThickness = root.findViewById(R.id.editTextThickness)
        spinnerTestingClass = root.findViewById(R.id.spinnerTestingClass)
        spinnerIqiType = root.findViewById(R.id.spinnerIqiType)
        spinnerStandard = root.findViewById(R.id.spinnerStandard)
        spinnerTechnique = root.findViewById(R.id.spinnerTechnique)
        spinnerPlacement = root.findViewById(R.id.spinnerPlacement)
        buttonCalculate = root.findViewById(R.id.buttonCalculate)
        cardResult = root.findViewById(R.id.cardResult)
        textViewResult = root.findViewById(R.id.textViewResult)
        textViewMaterial = root.findViewById(R.id.textViewMaterial)
        textViewDetails = root.findViewById(R.id.textViewDetails)
    }

    private fun loadIQIData() {
        try {
            val inputStream = requireContext().assets.open("iqi_selection_data.json")
            val size = inputStream.available()
            val buffer = ByteArray(size)
            inputStream.read(buffer)
            inputStream.close()

            // Odstranění komentářů z JSON
            var jsonString = String(buffer, Charsets.UTF_8)
            jsonString = jsonString.replace(Regex("//.*"), "")
            jsonString = jsonString.replace("Infinity", "999999.0")

            iqiData = JSONObject(jsonString)
        } catch (ex: IOException) {
            Toast.makeText(requireContext(), "Chyba při načítání dat: ${ex.message}", Toast.LENGTH_LONG).show()
        } catch (ex: Exception) {
            Toast.makeText(requireContext(), "Chyba při parsování JSON: ${ex.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun setupSpinners() {
        // Materiály objektu
        val materials = listOf(
            "Vyberte materiál",
            "Ocel",
            "Železné materiály",
            "Hliník",
            "Hliník a jeho slitiny",
            "Měď",
            "Zinek",
            "Cín a jejich slitiny",
            "Titan",
            "Titan a jeho slitiny"
        )
        setupSpinner(spinnerMaterial, materials)

        // Třída zkoušky
        val testingClasses = listOf("Vyberte třídu", "A", "B")
        setupSpinner(spinnerTestingClass, testingClasses)

        // Typ měrky
        val iqiTypes = listOf("Vyberte typ", "Drátová", "Stupeň/otvor")
        setupSpinner(spinnerIqiType, iqiTypes)

        // Normy
        val standards = listOf(
            "Vyberte normu",
            "CSN_EN_ISO_17636-1",
            "CSN_EN_ISO_19232-3"
        )
        setupSpinner(spinnerStandard, standards)

        // Nastavení listeneru pro změnu normy
        spinnerStandard.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                updateDependentSpinners()
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        // Inicializace prázdných spinnerů - budou naplněny podle vybrané normy
        setupSpinner(spinnerTechnique, listOf("Vyberte nejdříve normu"))
        setupSpinner(spinnerPlacement, listOf("Vyberte nejdříve normu"))
    }

    private fun setupSpinner(spinner: Spinner, items: List<String>) {
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, items)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinner.adapter = adapter
    }

    private fun setupCalculateButton() {
        buttonCalculate.setOnClickListener {
            calculateIQI()
        }
    }

    private fun calculateIQI() {
        try {
            // Získání vstupních hodnot
            val materialObject = spinnerMaterial.selectedItem.toString()
            val thicknessText = editTextThickness.text.toString()
            val testingClass = spinnerTestingClass.selectedItem.toString()
            val iqiType = spinnerIqiType.selectedItem.toString()
            val standard = spinnerStandard.selectedItem.toString()
            val technique = spinnerTechnique.selectedItem.toString()
            val placement = spinnerPlacement.selectedItem.toString()

            // Validace vstupů
            if (materialObject.startsWith("Vyberte") ||
                thicknessText.isEmpty() ||
                testingClass.startsWith("Vyberte") ||
                iqiType.startsWith("Vyberte") ||
                standard.startsWith("Vyberte") ||
                technique.startsWith("Vyberte") ||
                placement.startsWith("Vyberte")) {

                Toast.makeText(requireContext(), "Vyplňte všechny povinné údaje", Toast.LENGTH_SHORT).show()
                return
            }

            val thickness = thicknessText.toFloatOrNull()
            if (thickness == null || thickness <= 0) {
                Toast.makeText(requireContext(), "Zadejte platnou tloušťku", Toast.LENGTH_SHORT).show()
                return
            }

            // Volání funkce pro určení IQI
            val result = determineIQI(materialObject, thickness, testingClass, iqiType, standard, technique, placement)

            if (result != null) {
                displayResult(result.first, result.second, standard, testingClass, iqiType, technique, placement)
            }

        } catch (e: Exception) {
            Toast.makeText(requireContext(), "Chyba při výpočtu: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun determineIQI(
        materialObject: String,
        thickness: Float,
        testingClass: String,
        iqiType: String,
        standard: String,
        technique: String,
        placement: String
    ): Pair<String, String>? {

        if (iqiData == null) {
            Toast.makeText(requireContext(), "Data nejsou načtena", Toast.LENGTH_SHORT).show()
            return null
        }

        try {
            val selectionData = iqiData!!.getJSONObject("iqi_selection_data")

            // Krok 2: Určit materiál měrky na základě materiálu objektu
            val materialMappings = selectionData.getJSONArray("iqi_material_mapping")
            var iqiMaterialCode: String? = null

            for (i in 0 until materialMappings.length()) {
                val mapping = materialMappings.getJSONObject(i)
                val objectMaterials = mapping.getJSONArray("object_materials")

                for (j in 0 until objectMaterials.length()) {
                    if (objectMaterials.getString(j) == materialObject) {
                        iqiMaterialCode = mapping.getString("iqi_material_code")
                        break
                    }
                }
                if (iqiMaterialCode != null) break
            }

            if (iqiMaterialCode == null) {
                Toast.makeText(requireContext(), "Materiál objektu nebyl nalezen v mapování měrek", Toast.LENGTH_LONG).show()
                return null
            }

            // Krok 3: Najít relevantní standard
            if (!selectionData.has(standard)) {
                Toast.makeText(requireContext(), "Zadaná norma nebyla nalezena", Toast.LENGTH_SHORT).show()
                return null
            }

            val standardData = selectionData.getJSONObject(standard)
            val tables = standardData.getJSONArray("tables")

            // Krok 4: Filtrovat tabulky podle zadaných parametrů
            var selectedTable: JSONObject? = null

            for (i in 0 until tables.length()) {
                val table = tables.getJSONObject(i)

                // Flexibilní mapování pro techniku
                val techniqueMatch = when {
                    table.getString("technique") == technique -> true
                    technique == "Jednostěnná" && table.getString("technique") == "Prozařování přes jednu stěnu" -> true
                    technique == "Prozařování přes jednu stěnu" && table.getString("technique") == "Jednostěnná" -> true
                    technique == "Dvojstěnná" && table.getString("technique") == "Dvojstěnná" -> true
                    else -> false
                }

                // Flexibilní mapování pro umístění
                val placementMatch = when {
                    table.getString("iqi_placement") == placement -> true
                    placement == "Na straně zdroje" && table.getString("iqi_placement") == "Na straně zdroje záření" -> true
                    placement == "Na straně zdroje záření" && table.getString("iqi_placement") == "Na straně zdroje" -> true
                    else -> false
                }

                if (table.getString("iqi_type") == iqiType &&
                    table.getString("testing_class") == testingClass &&
                    techniqueMatch &&
                    placementMatch) {

                    selectedTable = table
                    break
                }
            }

            if (selectedTable == null) {
                val debugMessage = "Nebyla nalezena tabulka pro:\nTyp: $iqiType\nTřída: $testingClass\nTechnika: $technique\nUmístění: $placement"
                Toast.makeText(requireContext(), debugMessage, Toast.LENGTH_LONG).show()
                return null
            }

            // Krok 5: Vyhledat hodnotu měrky v tabulce podle tloušťky
            val thicknessRanges = selectedTable.getJSONArray("thickness_ranges_mm")
            var iqiValue: String? = null

            for (i in 0 until thicknessRanges.length()) {
                val range = thicknessRanges.getJSONObject(i)
                val from = range.getDouble("from").toFloat()
                val to = range.getDouble("to").toFloat()

                if (thickness > from && thickness <= to) {
                    iqiValue = range.getString("iqi_value")
                    break
                }
            }

            if (iqiValue == null) {
                Toast.makeText(requireContext(), "Hodnota měrky nebyla nalezena pro zadanou tloušťku", Toast.LENGTH_LONG).show()
                return null
            }

            return Pair(iqiValue, iqiMaterialCode)

        } catch (e: Exception) {
            Toast.makeText(requireContext(), "Chyba při zpracování dat: ${e.message}", Toast.LENGTH_LONG).show()
            return null
        }
    }

    private fun displayResult(
        iqiValue: String,
        iqiMaterialCode: String,
        standard: String,
        testingClass: String,
        iqiType: String,
        technique: String,
        placement: String
    ) {
        textViewResult.text = "Požadovaná IQI měrka: $iqiValue"
        textViewMaterial.text = "Materiál měrky: $iqiMaterialCode"
        textViewDetails.text = "Norma: $standard\nTřída: $testingClass\nTyp: $iqiType\nTechnika: $technique\nUmístění: $placement"

        cardResult.visibility = View.VISIBLE
    }
}