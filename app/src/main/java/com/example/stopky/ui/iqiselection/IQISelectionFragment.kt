package com.example.stopky.ui.iqiselection

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.cardview.widget.CardView
import androidx.fragment.app.Fragment
import com.example.stopky.R
import org.json.JSONObject
import java.io.IOException

class IQISelectionFragment : Fragment() {

    // UI prvky
    private lateinit var spinnerMaterial: Spinner
    private lateinit var editTextThickness: EditText
    private lateinit var spinnerTestingClass: Spinner
    private lateinit var spinnerIqiType: Spinner
    private lateinit var spinnerStandard: Spinner
    private lateinit var spinnerTechnique: Spinner
    private lateinit var spinnerPlacement: Spinner
    private lateinit var buttonCalculate: Button
    private lateinit var cardResult: CardView
    private lateinit var textViewResult: TextView
    private lateinit var textViewMaterial: TextView
    private lateinit var textViewDetails: TextView

    // Data
    private var iqiData: JSONObject? = null
    private lateinit var sharedPreferences: SharedPreferences
    private var pendingTechniqueRestore: String? = null
    private var pendingPlacementRestore: String? = null

    companion object {
        private const val PREFS_NAME = "IQISelectionPrefs"
        private const val KEY_MATERIAL = "material"
        private const val KEY_THICKNESS = "thickness"
        private const val KEY_TESTING_CLASS = "testing_class"
        private const val KEY_IQI_TYPE = "iqi_type"
        private const val KEY_STANDARD = "standard"
        private const val KEY_TECHNIQUE = "technique"
        private const val KEY_PLACEMENT = "placement"

        // Klíče pro mapování hodnot
        private const val TECHNIQUE_SINGLE = "single_wall"
        private const val TECHNIQUE_SINGLE_ISO = "single_wall_iso"
        private const val TECHNIQUE_DOUBLE = "double_wall"
        private const val PLACEMENT_SOURCE = "source_side"
        private const val PLACEMENT_SOURCE_ISO = "source_side_iso"
        private const val PLACEMENT_FILM = "film_side"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return try {
            val root = inflater.inflate(R.layout.fragment_wire_iqi_select, container, false)

            // Inicializace SharedPreferences
            sharedPreferences = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

            initializeViews(root)
            setupSpinners()
            setupAutoCalculation()
            loadIQIData()
            restoreSavedValues()

            root
        } catch (e: Exception) {
            Toast.makeText(requireContext(), getString(R.string.error_initialization, e.message), Toast.LENGTH_LONG).show()
            inflater.inflate(android.R.layout.simple_list_item_1, container, false)
        }
    }

    private fun initializeViews(root: View) {
        try {
            spinnerMaterial = root.findViewById(R.id.spinnerMaterial)
            editTextThickness = root.findViewById(R.id.editTextThickness)
            spinnerTestingClass = root.findViewById(R.id.spinnerTestingClass)
            spinnerIqiType = root.findViewById(R.id.spinnerIqiType)
            spinnerStandard = root.findViewById(R.id.spinnerStandard)
            spinnerTechnique = root.findViewById(R.id.spinnerTechnique)
            spinnerPlacement = root.findViewById(R.id.spinnerPlacement)
            buttonCalculate = root.findViewById(R.id.buttonCalculate)
            cardResult = root.findViewById(R.id.cardResult)
            textViewResult = root.findViewById(R.id.textViewResult)
            textViewMaterial = root.findViewById(R.id.textViewMaterial)
            textViewDetails = root.findViewById(R.id.textViewDetails)

            // Ověř, že všechny views byly nalezeny
            if (spinnerMaterial == null || spinnerStandard == null || spinnerTechnique == null || spinnerPlacement == null) {
                throw Exception(getString(R.string.error_ui_elements_not_found))
            }
        } catch (e: Exception) {
            Toast.makeText(requireContext(), getString(R.string.error_ui_initialization, e.message), Toast.LENGTH_LONG).show()
            throw e
        }
    }

    private fun loadIQIData() {
        try {
            val inputStream = requireContext().assets.open("iqi_selection_data.json")
            val size = inputStream.available()
            val buffer = ByteArray(size)
            inputStream.read(buffer)
            inputStream.close()

            val jsonString = String(buffer, Charsets.UTF_8)

            // Validace JSON před parsováním
            if (jsonString.isBlank()) {
                throw Exception(getString(R.string.error_json_empty))
            }

            iqiData = JSONObject(jsonString)

            // Validace struktury
            if (!iqiData!!.has("iqi_selection_data")) {
                throw Exception(getString(R.string.error_json_missing_key))
            }

            // Po načtení dat aktualizuj spinnery a obnov uložené hodnoty
            view?.post {
                updateDependentSpinners()
                // Obnov hodnoty pro závislé spinnery až po jejich aktualizaci s větším zpožděním
                view?.postDelayed({
                    restoreDependentSpinnerValues()
                }, 500)
            }

        } catch (ex: IOException) {
            Toast.makeText(requireContext(), getString(R.string.error_loading_data, ex.message), Toast.LENGTH_LONG).show()
        } catch (ex: org.json.JSONException) {
            Toast.makeText(requireContext(), getString(R.string.error_parsing_json, ex.message), Toast.LENGTH_LONG).show()
        } catch (ex: Exception) {
            Toast.makeText(requireContext(), getString(R.string.error_general, ex.message), Toast.LENGTH_LONG).show()
        }
    }

    private fun setupSpinners() {
        // Materiály objektu
        val materials = listOf(
            getString(R.string.select_material),
            getString(R.string.material_steel),
            getString(R.string.material_iron),
            getString(R.string.material_aluminum),
            getString(R.string.material_aluminum_alloys),
            getString(R.string.material_copper),
            getString(R.string.material_zinc),
            getString(R.string.material_tin_alloys),
            getString(R.string.material_titanium),
            getString(R.string.material_titanium_alloys)
        )
        setupSpinner(spinnerMaterial, materials)

        // Třída zkoušky
        val testingClasses = listOf(
            getString(R.string.select_class),
            getString(R.string.class_a),
            getString(R.string.class_b)
        )
        setupSpinner(spinnerTestingClass, testingClasses)

        // Typ měrky
        val iqiTypes = listOf(
            getString(R.string.select_type),
            getString(R.string.iqi_type_wire),
            getString(R.string.iqi_type_step_hole)
        )
        setupSpinner(spinnerIqiType, iqiTypes)

        // Normy
        val standards = listOf(
            getString(R.string.select_standard),
            "CSN_EN_ISO_17636-1",
            "CSN_EN_ISO_19232-3"
        )
        setupSpinner(spinnerStandard, standards)

        // Inicializace prázdných spinnerů - budou naplněny podle vybrané normy
        setupSpinner(spinnerTechnique, listOf(getString(R.string.loading)))
        setupSpinner(spinnerPlacement, listOf(getString(R.string.loading)))

        // Listener pro normu bude nastaven v setupAutoCalculation()
    }

    private fun setupSpinner(spinner: Spinner, items: List<String>) {
        try {
            if (items.isEmpty()) {
                return
            }

            val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, items)
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            spinner.adapter = adapter

            // Zkontroluj, zda máme pending hodnotu pro obnovení
            when (spinner) {
                spinnerTechnique -> {
                    if (!pendingTechniqueRestore.isNullOrEmpty()) {
                        view?.postDelayed({
                            val success = setSpinnerValue(spinner, pendingTechniqueRestore!!)
                            Toast.makeText(requireContext(), "Obnovuji techniku: $pendingTechniqueRestore, úspěch: $success", Toast.LENGTH_SHORT).show()
                            if (success) {
                                pendingTechniqueRestore = null
                            }
                        }, 100)
                    }
                }
                spinnerPlacement -> {
                    if (!pendingPlacementRestore.isNullOrEmpty()) {
                        view?.postDelayed({
                            val success = setSpinnerValue(spinner, pendingPlacementRestore!!)
                            Toast.makeText(requireContext(), "Obnovuji umístění: $pendingPlacementRestore, úspěch: $success", Toast.LENGTH_SHORT).show()
                            if (success) {
                                pendingPlacementRestore = null
                            }
                        }, 100)
                    }
                }
            }

        } catch (e: Exception) {
            // Fallback - vytvoř jednoduchý adapter s chybovou zprávou
            val fallbackItems = listOf(getString(R.string.loading_error))
            val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, fallbackItems)
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            spinner.adapter = adapter
        }
    }

    private fun updateDependentSpinners() {
        try {
            val selectedItem = spinnerStandard.selectedItem
            if (selectedItem == null) {
                setupSpinner(spinnerTechnique, listOf(getString(R.string.loading)))
                setupSpinner(spinnerPlacement, listOf(getString(R.string.loading)))
                return
            }

            val selectedStandard = selectedItem.toString()

            if (selectedStandard.startsWith(getString(R.string.select_standard).substring(0, 7)) ||
                selectedStandard.startsWith(getString(R.string.loading).substring(0, 4))) {
                setupSpinner(spinnerTechnique, listOf(getString(R.string.select_standard_first)))
                setupSpinner(spinnerPlacement, listOf(getString(R.string.select_standard_first)))
                return
            }

            // Získání dostupných možností z JSON podle vybrané normy
            val techniques = getAvailableTechniques(selectedStandard)
            val placements = getAvailablePlacements(selectedStandard)

            setupSpinner(spinnerTechnique, techniques)
            setupSpinner(spinnerPlacement, placements)
        } catch (e: Exception) {
            Toast.makeText(requireContext(), getString(R.string.error_updating_spinners, e.message), Toast.LENGTH_SHORT).show()
            setupSpinner(spinnerTechnique, listOf(getString(R.string.loading_error)))
            setupSpinner(spinnerPlacement, listOf(getString(R.string.loading_error)))
        }
    }

    private fun getAvailableTechniques(standard: String): List<String> {
        val techniques = mutableSetOf<String>()
        techniques.add(getString(R.string.select_technique))

        try {
            if (iqiData != null && iqiData!!.has("iqi_selection_data")) {
                val selectionData = iqiData!!.getJSONObject("iqi_selection_data")
                if (selectionData.has(standard)) {
                    val standardData = selectionData.getJSONObject(standard)
                    if (standardData.has("tables")) {
                        val tables = standardData.getJSONArray("tables")

                        for (i in 0 until tables.length()) {
                            val table = tables.getJSONObject(i)
                            if (table.has("technique")) {
                                techniques.add(table.getString("technique"))
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // V případě chyby vrátíme základní možnosti
            techniques.add(getString(R.string.loading_error))
        }

        return techniques.toList()
    }

    private fun getAvailablePlacements(standard: String): List<String> {
        val placements = mutableSetOf<String>()
        placements.add(getString(R.string.select_placement))

        try {
            if (iqiData != null && iqiData!!.has("iqi_selection_data")) {
                val selectionData = iqiData!!.getJSONObject("iqi_selection_data")
                if (selectionData.has(standard)) {
                    val standardData = selectionData.getJSONObject(standard)
                    if (standardData.has("tables")) {
                        val tables = standardData.getJSONArray("tables")

                        for (i in 0 until tables.length()) {
                            val table = tables.getJSONObject(i)
                            if (table.has("iqi_placement")) {
                                placements.add(table.getString("iqi_placement"))
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // V případě chyby vrátíme základní možnosti
            placements.add(getString(R.string.loading_error))
        }

        return placements.toList()
    }

    private fun setupAutoCalculation() {
        // Automatický výpočet při změně jakéhokoliv spinneru nebo editTextu
        val changeListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                saveCurrentValues()
                autoCalculateIQI()
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        spinnerMaterial.onItemSelectedListener = changeListener
        spinnerTestingClass.onItemSelectedListener = changeListener
        spinnerIqiType.onItemSelectedListener = changeListener
        // Speciální listenery pro závislé spinnery
        spinnerTechnique.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                val selectedText = parent?.getItemAtPosition(position)?.toString() ?: ""
                if (!selectedText.startsWith(getString(R.string.select_technique).substring(0, 7)) &&
                    !selectedText.startsWith(getString(R.string.loading).substring(0, 4)) &&
                    !selectedText.startsWith(getString(R.string.select_standard_first).substring(0, 7))) {
                    saveCurrentValues()
                }
                autoCalculateIQI()
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        spinnerPlacement.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                val selectedText = parent?.getItemAtPosition(position)?.toString() ?: ""
                if (!selectedText.startsWith(getString(R.string.select_placement).substring(0, 7)) &&
                    !selectedText.startsWith(getString(R.string.loading).substring(0, 4)) &&
                    !selectedText.startsWith(getString(R.string.select_standard_first).substring(0, 7))) {
                    saveCurrentValues()
                }
                autoCalculateIQI()
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        // Listener pro normu (speciální - aktualizuje závislé spinnery)
        spinnerStandard.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                updateDependentSpinners()
                saveCurrentValues()
                autoCalculateIQI()
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        // TextWatcher pro tloušťku
        editTextThickness.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                saveCurrentValues()
                autoCalculateIQI()
            }
        })

        // Skryjeme tlačítko - už není potřeba
        buttonCalculate.visibility = View.GONE
    }

    private fun saveCurrentValues() {
        try {
            with(sharedPreferences.edit()) {
                putString(KEY_MATERIAL, spinnerMaterial.selectedItem?.toString() ?: "")
                putString(KEY_THICKNESS, editTextThickness.text.toString())
                putString(KEY_TESTING_CLASS, spinnerTestingClass.selectedItem?.toString() ?: "")
                putString(KEY_IQI_TYPE, spinnerIqiType.selectedItem?.toString() ?: "")
                putString(KEY_STANDARD, spinnerStandard.selectedItem?.toString() ?: "")

                // Pro závislé spinnery uložíme pozice a standard
                val currentStandard = spinnerStandard.selectedItem?.toString() ?: ""
                val techniquePos = spinnerTechnique.selectedItemPosition
                val placementPos = spinnerPlacement.selectedItemPosition

                // Uložíme jen pokud jsou platné pozice (ne 0 = "Vyberte...")
                if (techniquePos > 0) {
                    putString("${KEY_TECHNIQUE}_standard", currentStandard)
                    putInt("${KEY_TECHNIQUE}_position", techniquePos)
                }
                if (placementPos > 0) {
                    putString("${KEY_PLACEMENT}_standard", currentStandard)
                    putInt("${KEY_PLACEMENT}_position", placementPos)
                }

                apply()
            }
        } catch (e: Exception) {
            // Ignoruj chyby při ukládání
        }
    }

    private fun getKeyForTechnique(text: String): String {
        return when (text) {
            getString(R.string.technique_single_wall) -> TECHNIQUE_SINGLE
            getString(R.string.technique_single_wall_iso) -> TECHNIQUE_SINGLE_ISO
            getString(R.string.technique_double_wall) -> TECHNIQUE_DOUBLE
            else -> ""
        }
    }

    private fun getKeyForPlacement(text: String): String {
        return when (text) {
            getString(R.string.placement_source_side) -> PLACEMENT_SOURCE
            getString(R.string.placement_source_side_iso) -> PLACEMENT_SOURCE_ISO
            getString(R.string.placement_film_side) -> PLACEMENT_FILM
            else -> ""
        }
    }

    private fun getTextForTechniqueKey(key: String): String {
        return when (key) {
            TECHNIQUE_SINGLE -> getString(R.string.technique_single_wall)
            TECHNIQUE_SINGLE_ISO -> getString(R.string.technique_single_wall_iso)
            TECHNIQUE_DOUBLE -> getString(R.string.technique_double_wall)
            else -> ""
        }
    }

    private fun getTextForPlacementKey(key: String): String {
        return when (key) {
            PLACEMENT_SOURCE -> getString(R.string.placement_source_side)
            PLACEMENT_SOURCE_ISO -> getString(R.string.placement_source_side_iso)
            PLACEMENT_FILM -> getString(R.string.placement_film_side)
            else -> ""
        }
    }

    private fun restoreSavedValues() {
        try {
            // Obnovení základních hodnot z SharedPreferences
            val savedMaterial = sharedPreferences.getString(KEY_MATERIAL, "")
            val savedThickness = sharedPreferences.getString(KEY_THICKNESS, "")
            val savedTestingClass = sharedPreferences.getString(KEY_TESTING_CLASS, "")
            val savedIqiType = sharedPreferences.getString(KEY_IQI_TYPE, "")
            val savedStandard = sharedPreferences.getString(KEY_STANDARD, "")

            // Nastavení hodnot do základních UI prvků
            setSpinnerValue(spinnerMaterial, savedMaterial)
            editTextThickness.setText(savedThickness)
            setSpinnerValue(spinnerTestingClass, savedTestingClass)
            setSpinnerValue(spinnerIqiType, savedIqiType)
            setSpinnerValue(spinnerStandard, savedStandard)

            // Uložíme si pending hodnoty pro závislé spinnery
            val savedTechniqueKey = sharedPreferences.getString(KEY_TECHNIQUE, "")
            val savedPlacementKey = sharedPreferences.getString(KEY_PLACEMENT, "")

            if (!savedTechniqueKey.isNullOrEmpty()) {
                pendingTechniqueRestore = getTextForTechniqueKey(savedTechniqueKey)
            }
            if (!savedPlacementKey.isNullOrEmpty()) {
                pendingPlacementRestore = getTextForPlacementKey(savedPlacementKey)
            }

        } catch (e: Exception) {
            // Ignoruj chyby při obnovování
        }
    }

    private fun restoreDependentSpinnerValues() {
        // Tato funkce už není potřeba - logika je v setupSpinner
        // Ponecháváme pro kompatibilitu
    }

    private fun setSpinnerValue(spinner: Spinner, value: String?): Boolean {
        if (value.isNullOrEmpty()) return false

        try {
            val adapter = spinner.adapter as? ArrayAdapter<String> ?: return false

            // Nejdříve zkusíme přesnou shodu
            for (i in 0 until adapter.count) {
                if (adapter.getItem(i) == value) {
                    spinner.setSelection(i)
                    return true
                }
            }

            // Pokud nenajdeme přesnou shodu, zkusíme částečnou shodu
            // (pro případ, kdy se změnily názvy v lokalizaci)
            for (i in 0 until adapter.count) {
                val item = adapter.getItem(i)
                if (item != null && value.length > 5 && item.contains(value.take(5))) {
                    spinner.setSelection(i)
                    return true
                }
            }

            return false

        } catch (e: Exception) {
            return false
        }
    }

    private fun setSpinnerPosition(spinner: Spinner, position: Int): Boolean {
        try {
            val adapter = spinner.adapter as? ArrayAdapter<String> ?: return false
            if (position >= 0 && position < adapter.count) {
                spinner.setSelection(position)
                return true
            }
            return false
        } catch (e: Exception) {
            return false
        }
    }

    private fun autoCalculateIQI() {
        try {
            // Zkontroluj, zda jsou všechny hodnoty vyplněny
            val materialObject = spinnerMaterial.selectedItem?.toString() ?: ""
            val thicknessText = editTextThickness.text.toString()
            val testingClass = spinnerTestingClass.selectedItem?.toString() ?: ""
            val iqiType = spinnerIqiType.selectedItem?.toString() ?: ""
            val standard = spinnerStandard.selectedItem?.toString() ?: ""
            val technique = spinnerTechnique.selectedItem?.toString() ?: ""
            val placement = spinnerPlacement.selectedItem?.toString() ?: ""

            // Pokud nejsou všechny hodnoty vyplněny, skryj výsledek
            if (materialObject.startsWith(getString(R.string.select_material).substring(0, 7)) ||
                materialObject.startsWith(getString(R.string.loading).substring(0, 4)) ||
                thicknessText.isEmpty() ||
                testingClass.startsWith(getString(R.string.select_class).substring(0, 7)) ||
                iqiType.startsWith(getString(R.string.select_type).substring(0, 7)) ||
                standard.startsWith(getString(R.string.select_standard).substring(0, 7)) ||
                technique.startsWith(getString(R.string.select_technique).substring(0, 7)) ||
                technique.startsWith(getString(R.string.loading).substring(0, 4)) ||
                placement.startsWith(getString(R.string.select_placement).substring(0, 7)) ||
                placement.startsWith(getString(R.string.loading).substring(0, 4))) {

                cardResult.visibility = View.GONE
                return
            }

            val thickness = thicknessText.toFloatOrNull()
            if (thickness == null || thickness <= 0) {
                cardResult.visibility = View.GONE
                return
            }

            // Varování pro měrky na straně filmu
            if (placement == getString(R.string.placement_film_side)) {
                Toast.makeText(requireContext(),
                    getString(R.string.film_side_warning),
                    Toast.LENGTH_LONG).show()
            }

            // Volání funkce pro určení IQI
            val result = determineIQI(materialObject, thickness, testingClass, iqiType, standard, technique, placement)

            if (result != null) {
                val recommendedSet = getRecommendedIQISet(result.first, result.second, iqiType)
                displayResult(result.first, result.second, standard, testingClass, iqiType, technique, placement, recommendedSet)
            } else {
                cardResult.visibility = View.GONE
            }

        } catch (e: Exception) {
            cardResult.visibility = View.GONE
        }
    }



    private fun determineIQI(
        materialObject: String,
        thickness: Float,
        testingClass: String,
        iqiType: String,
        standard: String,
        technique: String,
        placement: String
    ): Pair<String, String>? {

        if (iqiData == null) {
            Toast.makeText(requireContext(), getString(R.string.error_data_not_loaded), Toast.LENGTH_SHORT).show()
            return null
        }

        try {
            val selectionData = iqiData!!.getJSONObject("iqi_selection_data")

            // Krok 2: Určit materiál měrky na základě materiálu objektu
            val materialMappings = selectionData.getJSONArray("iqi_material_mapping")
            var iqiMaterialCode: String? = null

            // Mapování lokalizovaných názvů na anglické názvy v JSON
            val materialMapping = mapOf(
                getString(R.string.material_steel) to "Ocel",
                getString(R.string.material_iron) to "Železné materiály",
                getString(R.string.material_aluminum) to "Hliník",
                getString(R.string.material_aluminum_alloys) to "Hliník a jeho slitiny",
                getString(R.string.material_copper) to "Měď",
                getString(R.string.material_zinc) to "Zinek",
                getString(R.string.material_tin_alloys) to "Cín a jejich slitiny",
                getString(R.string.material_titanium) to "Titan",
                getString(R.string.material_titanium_alloys) to "Titan a jeho slitiny"
            )

            val jsonMaterialName = materialMapping[materialObject] ?: materialObject

            for (i in 0 until materialMappings.length()) {
                val mapping = materialMappings.getJSONObject(i)
                val objectMaterials = mapping.getJSONArray("object_materials")

                for (j in 0 until objectMaterials.length()) {
                    if (objectMaterials.getString(j) == jsonMaterialName) {
                        iqiMaterialCode = mapping.getString("iqi_material_code")
                        break
                    }
                }
                if (iqiMaterialCode != null) break
            }

            if (iqiMaterialCode == null) {
                Toast.makeText(requireContext(), getString(R.string.error_material_not_found), Toast.LENGTH_LONG).show()
                return null
            }

            // Krok 3: Najít relevantní standard
            if (!selectionData.has(standard)) {
                Toast.makeText(requireContext(), getString(R.string.error_standard_not_found), Toast.LENGTH_SHORT).show()
                return null
            }

            val standardData = selectionData.getJSONObject(standard)
            val tables = standardData.getJSONArray("tables")

            // Krok 4: Filtrovat tabulky podle zadaných parametrů
            var selectedTable: JSONObject? = null

            // Mapování lokalizovaných názvů na JSON hodnoty
            val techniqueMapping = mapOf(
                getString(R.string.technique_single_wall) to "Jednostěnná",
                getString(R.string.technique_single_wall_iso) to "Prozařování přes jednu stěnu",
                getString(R.string.technique_double_wall) to "Dvojstěnná"
            )

            val placementMapping = mapOf(
                getString(R.string.placement_source_side) to "Na straně zdroje",
                getString(R.string.placement_source_side_iso) to "Na straně zdroje záření",
                getString(R.string.placement_film_side) to "Na straně filmu"
            )

            val iqiTypeMapping = mapOf(
                getString(R.string.iqi_type_wire) to "Drátová",
                getString(R.string.iqi_type_step_hole) to "Stupeň/otvor"
            )

            val jsonTechnique = techniqueMapping[technique] ?: technique
            val jsonPlacement = placementMapping[placement] ?: placement
            val jsonIqiType = iqiTypeMapping[iqiType] ?: iqiType

            for (i in 0 until tables.length()) {
                val table = tables.getJSONObject(i)

                // Flexibilní mapování pro techniku
                val techniqueMatch = when {
                    table.getString("technique") == jsonTechnique -> true
                    jsonTechnique == "Jednostěnná" && table.getString("technique") == "Prozařování přes jednu stěnu" -> true
                    jsonTechnique == "Prozařování přes jednu stěnu" && table.getString("technique") == "Jednostěnná" -> true
                    jsonTechnique == "Dvojstěnná" && table.getString("technique") == "Dvojstěnná" -> true
                    else -> false
                }

                // Flexibilní mapování pro umístění
                val placementMatch = when {
                    table.getString("iqi_placement") == jsonPlacement -> true
                    jsonPlacement == "Na straně zdroje" && table.getString("iqi_placement") == "Na straně zdroje záření" -> true
                    jsonPlacement == "Na straně zdroje záření" && table.getString("iqi_placement") == "Na straně zdroje" -> true
                    else -> false
                }

                if (table.getString("iqi_type") == jsonIqiType &&
                    table.getString("testing_class") == testingClass &&
                    techniqueMatch &&
                    placementMatch) {

                    selectedTable = table
                    break
                }
            }

            if (selectedTable == null) {
                val debugMessage = getString(R.string.debug_table_search, jsonIqiType, testingClass, jsonTechnique, jsonPlacement)
                Toast.makeText(requireContext(), debugMessage, Toast.LENGTH_LONG).show()
                return null
            }

            // Krok 5: Vyhledat hodnotu měrky v tabulce podle tloušťky
            val thicknessRanges = selectedTable.getJSONArray("thickness_ranges_mm")
            var iqiValue: String? = null

            for (i in 0 until thicknessRanges.length()) {
                val range = thicknessRanges.getJSONObject(i)
                val from = range.getDouble("from").toFloat()
                val to = range.getDouble("to").toFloat()

                if (thickness > from && thickness <= to) {
                    iqiValue = range.getString("iqi_value")
                    break
                }
            }

            if (iqiValue == null) {
                Toast.makeText(requireContext(), getString(R.string.error_thickness_not_found), Toast.LENGTH_LONG).show()
                return null
            }

            return Pair(iqiValue, iqiMaterialCode)

        } catch (e: Exception) {
            Toast.makeText(requireContext(), getString(R.string.error_data_processing, e.message), Toast.LENGTH_LONG).show()
            return null
        }
    }

    private fun getRecommendedIQISet(iqiValue: String, materialCode: String, iqiType: String): String? {
        try {
            if (iqiData == null) return null

            val selectionData = iqiData!!.getJSONObject("iqi_selection_data")
            val iqiSetsData = selectionData.getJSONObject("iqi_sets_data")

            val setsKey = if (iqiType == "Drátová") "wire_iqi_sets" else "step_hole_iqi_sets"
            val materialSets = iqiSetsData.getJSONObject(setsKey)

            // Určit správný materiálový klíč (s nebo bez _film)
            val materialKey = if (iqiValue.endsWith("F")) "${materialCode}_film" else materialCode

            if (!materialSets.has(materialKey)) return null

            val sets = materialSets.getJSONArray(materialKey)
            var bestSet: String? = null
            var isInRecommendedRange = false

            for (i in 0 until sets.length()) {
                val set = sets.getJSONObject(i)
                val setName = set.getString("set_name")

                val itemsKey = if (iqiType == "Drátová") "wires" else "holes"
                val items = set.getJSONArray(itemsKey)
                val recommendedRange = set.getJSONArray("recommended_range")

                // Zkontroluj, zda se IQI hodnota nachází v této sadě
                var containsValue = false
                for (j in 0 until items.length()) {
                    if (items.getString(j) == iqiValue) {
                        containsValue = true
                        break
                    }
                }

                if (containsValue) {
                    bestSet = setName

                    // Zkontroluj, zda je v doporučeném rozsahu
                    for (j in 0 until recommendedRange.length()) {
                        if (recommendedRange.getString(j) == iqiValue) {
                            isInRecommendedRange = true
                            return "$setName (doporučeno - optimální rozsah)"
                        }
                    }
                }
            }

            return if (bestSet != null) {
                "$bestSet (dostupné, ale mimo optimální rozsah)"
            } else {
                null
            }

        } catch (e: Exception) {
            return null
        }
    }

    private fun displayResult(
        iqiValue: String,
        iqiMaterialCode: String,
        standard: String,
        testingClass: String,
        iqiType: String,
        technique: String,
        placement: String,
        recommendedSet: String?
    ) {
        // Zvýraznění měrek s písmenkem F
        val resultText = if (iqiValue.endsWith("F")) {
            getString(R.string.iqi_required_gauge_film, iqiValue)
        } else {
            getString(R.string.iqi_required_gauge, iqiValue)
        }

        textViewResult.text = resultText
        textViewMaterial.text = getString(R.string.iqi_material_code, iqiMaterialCode)

        var detailsText = getString(R.string.debug_parameters, standard, testingClass, iqiType, technique, placement)

        if (iqiValue.endsWith("F")) {
            detailsText += "\n\n" + getString(R.string.film_side_explanation)
        }

        if (recommendedSet != null) {
            detailsText += "\n\n" + getString(R.string.iqi_recommended_set, recommendedSet)
        } else {
            detailsText += "\n\n" + getString(R.string.iqi_no_set_found, iqiValue)
        }

        textViewDetails.text = detailsText

        cardResult.visibility = View.VISIBLE
    }
}